import { randomUUID } from 'node:crypto';

import { InsertablePage } from '../../src/drizzle/types/entity.types';
import { app, pageRepo } from './test-setup';

describe('PageRepo Integration Tests', () => {
    beforeAll(async () => {
        expect(app).toBeDefined();
        expect(pageRepo).toBeDefined();
    });

    describe('insertManyPages', () => {
        it('должен создать несколько страниц одновременно', async () => {
            const insertablePages: InsertablePage[] = [
                {
                    id: randomUUID(),
                    serviceId: 'bulk-service-1',
                    entityId: 'bulk-entity-1',
                    schoolId: 'bulk-school-1',
                    ydoc: null,
                },
                {
                    id: randomUUID(),
                    serviceId: 'bulk-service-2',
                    entityId: 'bulk-entity-2',
                    schoolId: 'bulk-school-2',
                    ydoc: null,
                },
                {
                    id: randomUUID(),
                    serviceId: 'bulk-service-3',
                    entityId: 'bulk-entity-3',
                    schoolId: 'bulk-school-3',
                    ydoc: null,
                },
            ];

            const createdPages = await pageRepo.insertManyDocs(insertablePages);

            expect(createdPages).toHaveLength(3);
            expect(createdPages[0]).toBeDefined();
            expect(createdPages[0].id).toBe(insertablePages[0].id);
            expect(createdPages[0].serviceId).toBe('bulk-service-1');
            expect(createdPages[0].entityId).toBe('bulk-entity-1');
            expect(createdPages[0].schoolId).toBe('bulk-school-1');

            expect(createdPages[1]).toBeDefined();
            expect(createdPages[1].id).toBe(insertablePages[1].id);
            expect(createdPages[1].serviceId).toBe('bulk-service-2');

            expect(createdPages[2]).toBeDefined();
            expect(createdPages[2].id).toBe(insertablePages[2].id);
            expect(createdPages[2].serviceId).toBe('bulk-service-3');

            // Проверяем, что страницы действительно созданы в БД
            for (const page of createdPages) {
                const foundPage = await pageRepo.findById(page.id);
                expect(foundPage).toBeDefined();
                expect(foundPage.id).toBe(page.id);
            }
        });

        it('должен создать пустой массив при передаче пустого массива', async () => {
            const createdPages = await pageRepo.insertManyDocs([]);
            expect(createdPages).toHaveLength(0);
        });
    });

    describe('deleteManyPages', () => {
        it('должен удалить несколько страниц одновременно', async () => {
            // Создаем тестовые страницы
            const insertablePages: InsertablePage[] = [
                {
                    id: randomUUID(),
                    serviceId: 'delete-service-1',
                    entityId: 'delete-entity-1',
                    schoolId: 'delete-school-1',
                    ydoc: null,
                },
                {
                    id: randomUUID(),
                    serviceId: 'delete-service-2',
                    entityId: 'delete-entity-2',
                    schoolId: 'delete-school-2',
                    ydoc: null,
                },
            ];

            const createdPages = await pageRepo.insertManyDocs(insertablePages);
            expect(createdPages).toHaveLength(2);

            // Проверяем, что страницы существуют
            for (const page of createdPages) {
                const exists = await pageRepo.exists(page.id);
                expect(exists).toBe(true);
            }

            // Удаляем страницы
            const pageIds = createdPages.map((page) => page.id);
            await pageRepo.deleteManyDocs(pageIds);

            // Проверяем, что страницы удалены
            for (const pageId of pageIds) {
                const exists = await pageRepo.exists(pageId);
                expect(exists).toBe(false);
            }
        });

        it('должен корректно обработать удаление несуществующих страниц', async () => {
            const nonExistentIds = [randomUUID(), randomUUID()];

            // Не должно выбрасывать ошибку
            await expect(pageRepo.deleteManyDocs(nonExistentIds)).resolves.not.toThrow();
        });

        it('должен корректно обработать пустой массив ID', async () => {
            await expect(pageRepo.deleteManyDocs([])).resolves.not.toThrow();
        });
    });

    describe('exists', () => {
        it('должен вернуть true для существующей страницы', async () => {
            const insertablePage: InsertablePage = {
                id: randomUUID(),
                serviceId: 'exists-service',
                entityId: 'exists-entity',
                schoolId: 'exists-school',
                ydoc: null,
            };

            const createdPage = await pageRepo.insertDoc(insertablePage);
            expect(createdPage).toBeDefined();

            const exists = await pageRepo.exists(createdPage.id);
            expect(exists).toBe(true);
        });

        it('должен вернуть false для несуществующей страницы', async () => {
            const nonExistentId = randomUUID();
            const exists = await pageRepo.exists(nonExistentId);
            expect(exists).toBe(false);
        });
    });
});
