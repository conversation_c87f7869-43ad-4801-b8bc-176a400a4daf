import { setTimeout } from 'node:timers/promises';
import { Logger } from '@nestjs/common';
import { Args, ID, Mutation, Query, Resolver } from '@skillspace/graphql';

import { DocumentService } from '../../application/services/document.service';
import { PageService } from '../../application/services/page.service';
import { DocumentOutput } from './document.output';
import { PageOutput } from './page.output';

@Resolver(() => PageOutput)
export class PageResolver {
    private readonly logger = new Logger(PageResolver.name);

    constructor(
        private readonly docService: DocumentService,
        private readonly pageService: PageService,
    ) {}

    @Query(() => DocumentOutput, { name: 'document' })
    async getDocument(
        @Args('docId', { type: () => ID })
        docId: string,
    ): Promise<PageOutput> {
        this.logger.log(`Getting document by ID: ${docId}`);
        return await this.docService.findById(docId);
    }

    @Mutation(() => Boolean, { name: 'addPage' })
    async addPage(
        @Args('docId', { type: () => ID })
        docId: string,
    ): Promise<boolean> {
        this.logger.log(`Adding page into doc: ${docId}`);
        await this.pageService.create(docId);
        return true;
    }

    @Mutation(() => Boolean, { name: 'deletePage' })
    async deletePage(
        @Args('pageId', { type: () => ID })
        pageId: string,
    ): Promise<boolean> {
        this.logger.log(`Deleting doc: ${pageId}`);
        await this.pageService.forceDelete(pageId);
        return true;
    }

    @Mutation(() => Boolean, { name: 'publicDocument' })
    async publicDocument(
        @Args('docId', { type: () => ID })
        docId: string,
    ): Promise<boolean> {
        this.logger.log(`Public doc: ${docId}`);
        await setTimeout();
        return true;
    }
}
