import { Modu<PERSON> } from '@nestjs/common';

import { DocumentService } from './application/services/document.service';
import { PageService } from './application/services/page.service';
import { DocRepo } from './infrastructure/repositories/document.repo';
import { DratPageRepo } from './infrastructure/repositories/draft-page.repo';
import { PublicPageRepo } from './infrastructure/repositories/public-page.repo';
import { DOC_REPO, DRAFT_PAGE_REPO, PUBLIC_PAGE_REPO } from './injects';
import { DocManagement } from './presentation/documents.consumer';
import { PageResolver } from './presentation/graphql/documents.resolver';

@Module({
    providers: [
        { provide: DRAFT_PAGE_REPO, useClass: DratPageRepo },
        { provide: PUBLIC_PAGE_REPO, useClass: PublicPageRepo },
        { provide: DOC_REPO, useClass: DocRepo },
        PageService,
        DocumentService,
        PageResolver,
        DocManagement,
    ],
    exports: [PageService, DRAFT_PAGE_REPO],
})
export class EditorModule {}
