import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { IsString } from 'class-validator';
import { v7 as uuidv7 } from 'uuid';

import { InsertablePage, Page, UpdatablePage } from '../../../../drizzle/types/entity.types';
import { DRAFT_PAGE_REPO } from '../../injects';
import {
    CreatePageRequest,
    CreatePageResponse,
    IPageRepo,
    RemoveManyPagesResponse,
} from '../repositories/page-repository.interface';

export class CreatePageDto {
    @IsString()
    docId: string;
}

@Injectable()
export class PageService {
    private readonly logger = new Logger(PageService.name);

    constructor(
        @Inject(DRAFT_PAGE_REPO)
        private pageRepo: IPageRepo,
    ) {}

    async findById(pageId: string): Promise<Page> {
        const page = await this.pageRepo.findById(pageId);
        if (!page) {
            throw new NotFoundException(`Page with ID ${pageId} not found`);
        }
        return page;
    }

    /**
     * Поиск существующей страницы по ID
     */
    async findByIdOrNull(pageId: string): Promise<Page | null> {
        try {
            const result = await this.pageRepo.findById(pageId);
            if (!result) {
                this.logger.debug(`Страница не найдена для ID: ${pageId}`);
                return null;
            }
            this.logger.debug(`Найдена существующая страница с ID: ${pageId}`);
            return result;
        } catch (error) {
            this.logger.error(error, `Ошибка загрузки документа (pageId: ${pageId}):`);
        }
    }

    async create(docId: string, pageId?: string): Promise<Page> {
        const insertableData: InsertablePage = {
            id: pageId ?? uuidv7(),
            docId,
            ydoc: null, // заполняется через WebSocket/Hocuspocus
        };

        try {
            const page = await this.pageRepo.insertPage(insertableData);
            this.logger.debug(`Создана новая страница с ID: ${pageId}`);
            return page;
        } catch (createError) {
            this.logger.error(createError, `Ошибка создания страницы для ${pageId}:`);
            throw createError;
        }
    }

    async update(pageId: string, updatePageDto: UpdatablePage): Promise<void> {
        try {
            await this.pageRepo.updatePage(updatePageDto, pageId);
            this.logger.debug(`Обновлена страница с ID: ${pageId}`);
        } catch (createError) {
            this.logger.error(createError, `Ошибка создания страницы для ${pageId}:`);
            throw createError;
        }
    }

    async forceDelete(pageId: string): Promise<void> {
        this.logger.log(`Force deleting page ${pageId}`);

        await this.findById(pageId);
        await this.pageRepo.deletePage(pageId);
        this.logger.log(`Force deleted page ${pageId}`);
    }

    async createManyPages(pages: CreatePageRequest[]): Promise<CreatePageResponse[]> {
        this.logger.log(`Creating ${pages.length} pages`);

        const insertablePages: InsertablePage[] = pages.map((pageRequest) => ({
            id: uuidv7(),
            docId: pageRequest.docId,
            ydoc: null, // заполняется через WebSocket/Hocuspocus
        }));

        try {
            const createdPages = await this.pageRepo.insertManyPages(insertablePages);

            this.logger.log(`Successfully created ${createdPages.length} pages`);
            return createdPages.map((page) => ({
                pageId: page.id,
                docId: page.docId,
            }));
        } catch (error) {
            this.logger.error(error, 'Error creating multiple pages:');
            throw error;
        }
    }

    async removeManyPages(pageIds: string[]): Promise<RemoveManyPagesResponse> {
        this.logger.log(`Removing ${pageIds.length} pages`);

        const removedPageIds: string[] = [];

        try {
            // Проверяем существование всех страниц и собираем существующие
            for (const pageId of pageIds) {
                const exists = await this.pageRepo.exists(pageId);
                if (exists) {
                    removedPageIds.push(pageId);
                } else {
                    this.logger.warn(`Page with ID ${pageId} not found, skipping`);
                }
            }

            // Удаляем только существующие страницы
            if (removedPageIds.length > 0) {
                await this.pageRepo.deleteManyPages(removedPageIds);
                this.logger.log(`Successfully removed ${removedPageIds.length} pages`);
            }

            return {
                success: true,
                removedPageIds,
            };
        } catch (error) {
            this.logger.error(error, 'Error removing multiple pages:');
            throw error;
        }
    }
}
