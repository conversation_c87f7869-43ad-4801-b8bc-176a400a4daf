import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { IsNotEmpty, IsString } from 'class-validator';
import { v7 as uuidv7 } from 'uuid';

import { Doc, InsertableDoc, UpdatableDoc } from '../../../../drizzle/schema/docs';
import { DOC_REPO } from '../../injects';
import {
    CreateDocumentRequest,
    CreateDocumentResponse,
    IDocumentRepo,
    RemoveManyDocumentsResponse,
} from '../repositories/documents-repository.interface';

export class CreateDocumentDto {
    @IsNotEmpty()
    @IsString()
    schoolId: string;

    @IsNotEmpty()
    @IsString()
    serviceId: string;

    @IsNotEmpty()
    @IsString()
    entityId: string;
}

@Injectable()
export class DocumentService {
    private readonly logger = new Logger(DocumentService.name);

    constructor(
        @Inject(DOC_REPO)
        private docRepo: IDocumentRepo,
    ) {}

    async findById(docId: string): Promise<Doc> {
        const doc = await this.docRepo.findById(docId);
        if (!doc) {
            throw new NotFoundException(`Document with ID ${docId} not found`);
        }
        return doc;
    }

    /**
     * Поиск существующей страницы по ID
     */
    async findByIdOrNull(docId: string): Promise<Doc | null> {
        try {
            const result = await this.docRepo.findById(docId);
            if (!result) {
                this.logger.debug(`Страница не найдена для ID: ${docId}`);
                return null;
            }
            this.logger.debug(`Найдена существующая страница с ID: ${docId}`);
            return result;
        } catch (error) {
            this.logger.error(error, `Ошибка загрузки документа (docId: ${docId}):`);
        }
    }

    async create(createDocDto: CreateDocumentDto, docId?: string): Promise<Doc> {
        const insertableData: InsertableDoc = {
            id: docId ?? uuidv7(),
            serviceId: createDocDto.serviceId,
            entityId: createDocDto.entityId,
            schoolId: createDocDto.schoolId,
        };

        try {
            const doc = await this.docRepo.insertDoc(insertableData);
            this.logger.debug(`Создана новая страница с ID: ${docId}`);
            return doc;
        } catch (createError) {
            this.logger.error(createError, `Ошибка создания страницы для ${docId}:`);
            throw createError;
        }
    }

    async update(docId: string, updateDto: UpdatableDoc): Promise<void> {
        try {
            await this.docRepo.updateDoc(updateDto, docId);
            this.logger.debug(`Обновлен документ с ID: ${docId}`);
        } catch (createError) {
            this.logger.error(createError, `Ошибка обновления документа для ${docId}:`);
            throw createError;
        }
    }

    async forceDelete(docId: string): Promise<void> {
        this.logger.log(`Force deleting doc ${docId}`);

        await this.findById(docId);
        await this.docRepo.deleteDoc(docId);
        this.logger.log(`Force deleted doc ${docId}`);
    }

    async createManyDocs(docs: CreateDocumentRequest[]): Promise<CreateDocumentResponse[]> {
        this.logger.log(`Creating ${docs.length} docs`);

        const insertableDocs: InsertableDoc[] = docs.map((docRequest) => ({
            id: uuidv7(),
            serviceId: docRequest.serviceId,
            entityId: docRequest.entityId,
            schoolId: docRequest.schoolId ?? 'unknown',
        }));

        try {
            const createdDocs = await this.docRepo.insertManyDocs(insertableDocs);

            this.logger.log(`Successfully created ${createdDocs.length} docs`);
            return createdDocs.map((doc) => ({
                docId: doc.id,
                schoolId: doc.schoolId,
                serviceId: doc.serviceId,
                entityId: doc.entityId,
            }));
        } catch (error) {
            this.logger.error(error, 'Error creating multiple docs:');
            throw error;
        }
    }

    async removeManyDocs(docIds: string[]): Promise<RemoveManyDocumentsResponse> {
        this.logger.log(`Removing ${docIds.length} docs`);

        const removedDocIds: string[] = [];

        try {
            // Проверяем существование всех страниц и собираем существующие
            for (const docId of docIds) {
                const exists = await this.docRepo.exists(docId);
                if (exists) {
                    removedDocIds.push(docId);
                } else {
                    this.logger.warn(`Doc with ID ${docId} not found, skipping`);
                }
            }

            // Удаляем только существующие документы
            if (removedDocIds.length > 0) {
                await this.docRepo.deleteManyDocs(removedDocIds);
                this.logger.log(`Successfully removed ${removedDocIds.length} docs`);
            }

            return {
                success: true,
                removedDocIds,
            };
        } catch (error) {
            this.logger.error(error, 'Error removing multiple docs:');
            throw error;
        }
    }
}
