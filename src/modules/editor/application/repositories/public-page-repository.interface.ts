import { DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { InsertablePublicPage, PublicPage, UpdatablePublicPage } from '../../../../drizzle/schema/public-pages';

export interface IPublicPageRepo {
    /**
     * Найти публичную страницу по ID
     */
    findById(pageId: string, trx?: DrizzleTransaction): Promise<PublicPage | undefined>;

    /**
     * Найти публичные страницы по docId
     */
    findByDocId(docId: string, trx?: DrizzleTransaction): Promise<PublicPage[]>;

    /**
     * Обновить одну публичную страницу
     */
    updatePage(updatablePage: UpdatablePublicPage, pageId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Обновить несколько публичных страниц
     */
    updatePages(updatePageData: UpdatablePublicPage, pageIds: string[], trx?: DrizzleTransaction): Promise<void>;

    /**
     * Вставить новую публичную страницу
     */
    insertPage(insertablePage: InsertablePublicPage, trx?: DrizzleTransaction): Promise<PublicPage | undefined>;

    /**
     * Вставить несколько публичных страниц
     */
    insertManyPages(insertablePages: InsertablePublicPage[], trx?: DrizzleTransaction): Promise<PublicPage[]>;

    /**
     * Удалить публичную страницу по ID
     */
    deletePage(pageId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Удалить несколько публичных страниц по ID
     */
    deleteManyPages(pageIds: string[], trx?: DrizzleTransaction): Promise<void>;

    /**
     * Проверить наличие публичной страницы в БД
     */
    exists(pageId: string): Promise<boolean>;
}
