import { DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { Doc, InsertableDoc, UpdatableDoc } from '../../../../drizzle/schema/docs';

// Интерфейсы для массовых операций со страницами
export interface CreateDocumentRequest {
    serviceId: string;
    entityId: string;
    schoolId: string;
}

export interface CreateManyDocumentsRequest {
    pages: CreateDocumentRequest[];
}

export interface CreateDocumentResponse extends CreateDocumentRequest {
    docId: string;
}

export interface RemoveManyPagesRequest {
    docIds: string[];
}

export interface RemoveManyDocumentsResponse {
    success: boolean;
    removedDocIds: string[];
}

export interface IDocumentRepo {
    /**
     * Найти документ по ID
     */
    findById(docId: string, trx?: DrizzleTransaction): Promise<Doc | undefined>;

    /**
     * Создать новый документ
     */
    insertDoc(insertableDoc: InsertableDoc, trx?: DrizzleTransaction): Promise<Doc | undefined>;

    /**
     * Обновить документ
     */
    updateDoc(updatableDoc: UpdatableDoc, docId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Вставить несколько документов
     */
    insertManyDocs(insertableDocs: InsertableDoc[], trx?: DrizzleTransaction): Promise<Doc[]>;

    /**
     * Удалить документ по ID
     */
    deleteDoc(docId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Удалить несколько документов по ID
     */
    deleteManyDocs(docIds: string[], trx?: DrizzleTransaction): Promise<void>;

    /**
     * Проверить наличие документа в БД
     */
    exists(docId: string): Promise<boolean>;
}
