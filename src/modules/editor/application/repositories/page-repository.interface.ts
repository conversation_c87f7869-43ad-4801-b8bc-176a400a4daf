import { DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { InsertablePublicPage, PublicPage, UpdatablePublicPage } from '../../../../drizzle/schema';

// Интерфейсы для массовых операций со страницами
export interface CreatePageRequest {
    docId: string;
}

export interface CreateManyPagesRequest {
    pages: CreatePageRequest[];
}

export interface CreatePageResponse extends CreatePageRequest {
    pageId: string;
}

export interface RemoveManyPagesRequest {
    pageIds: string[];
}

export interface RemoveManyPagesResponse {
    success: boolean;
    removedPageIds: string[];
}

export interface IPageRepo {
    /**
     * Найти страницу по ID
     */
    findById(pageId: string, trx?: DrizzleTransaction): Promise<PublicPage | undefined>;

    /**
     * Обновить одну страницу
     */
    updatePage(updatablePage: UpdatablePublicPage, pageId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Обновить несколько страниц
     */
    updatePages(updatePageData: UpdatablePublicPage, pageIds: string[], trx?: DrizzleTransaction): Promise<void>;

    /**
     * Вставить новую страницу
     */
    insertPage(insertablePage: InsertablePublicPage, trx?: DrizzleTransaction): Promise<PublicPage | undefined>;

    /**
     * Вставить несколько страниц
     */
    insertManyPages(insertablePages: InsertablePublicPage[], trx?: DrizzleTransaction): Promise<PublicPage[]>;

    /**
     * Удалить страницу по ID
     */
    deletePage(pageId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Удалить несколько страниц по ID
     */
    deleteManyPages(pageIds: string[], trx?: DrizzleTransaction): Promise<void>;

    /**
     * Проверить наличие страницы в БД
     */
    exists(pageId: string): Promise<boolean>;
}
