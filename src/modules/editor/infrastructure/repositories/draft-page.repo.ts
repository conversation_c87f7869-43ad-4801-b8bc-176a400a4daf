import { Inject, Injectable } from '@nestjs/common';
import { eq, inArray } from 'drizzle-orm';

import { DRIZZLE_DB } from '../../../../drizzle/drizzle.module';
import { DrizzleDB, DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { dbOrTx } from '../../../../drizzle/drizzle.utils';
import { DraftPage, draftPages, InsertableDraftPage, UpdatableDraftPage } from '../../../../drizzle/schema/draft-pages';

@Injectable()
export class DratPageRepo {
    constructor(@Inject(DRIZZLE_DB) private readonly db: DrizzleDB) {}

    async findById(draftId: string, trx?: DrizzleTransaction): Promise<DraftPage | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.select().from(draftPages).where(eq(draftPages.id, draftId)).limit(1);
        return result[0];
    }

    async updateDraft(updatablePage: UpdatableDraftPage, pageId: string, trx?: DrizzleTransaction): Promise<void> {
        return this.updatePages(updatablePage, [pageId], trx);
    }

    async insertDoc(insertablePage: InsertableDraftPage, trx?: DrizzleTransaction): Promise<DraftPage | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.insert(draftPages).values(insertablePage).returning().execute();
        return result?.[0];
    }

    async insertManyDocs(insertablePages: InsertableDraftPage[], trx?: DrizzleTransaction): Promise<DraftPage[]> {
        if (insertablePages.length === 0) {
            return [];
        }

        const db = dbOrTx(this.db, trx);
        const result = await db.insert(draftPages).values(insertablePages).returning().execute();
        return result;
    }

    async deleteDoc(pageId: string, trx?: DrizzleTransaction): Promise<void> {
        const db = dbOrTx(this.db, trx);
        await db.delete(draftPages).where(eq(draftPages.id, pageId)).execute();
    }

    async deleteManyDocs(pageIds: string[], trx?: DrizzleTransaction): Promise<void> {
        if (pageIds.length === 0) {
            return;
        }

        const db = dbOrTx(this.db, trx);
        await db.delete(draftPages).where(inArray(draftPages.id, pageIds)).execute();
    }

    async exists(pageId: string): Promise<boolean> {
        const result = await this.db
            .select({ id: draftPages.id })
            .from(draftPages)
            .where(eq(draftPages.id, pageId))
            .limit(1)
            .execute();

        return result.length > 0;
    }
}
