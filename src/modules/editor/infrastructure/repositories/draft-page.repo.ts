import { Inject, Injectable } from '@nestjs/common';
import { eq, inArray } from 'drizzle-orm';

import { DRIZZLE_DB } from '../../../../drizzle/drizzle.module';
import { DrizzleDB, DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { dbOrTx } from '../../../../drizzle/drizzle.utils';
import { DraftPage, draftPages, InsertableDraftPage, UpdatableDraftPage } from '../../../../drizzle/schema/draft-pages';
import { IDraftPageRepo } from '../../application/repositories/draft-page-repository.interface';

@Injectable()
export class DraftPageRepo implements IDraftPageRepo {
    constructor(@Inject(DRIZZLE_DB) private readonly db: DrizzleDB) {}

    async findById(draftId: string, trx?: DrizzleTransaction): Promise<DraftPage | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.select().from(draftPages).where(eq(draftPages.id, draftId)).limit(1);
        return result[0];
    }

    async findByDocId(docId: string, trx?: DrizzleTransaction): Promise<DraftPage[]> {
        const db = dbOrTx(this.db, trx);
        const result = await db.select().from(draftPages).where(eq(draftPages.docId, docId));
        return result;
    }

    async updatePage(updatablePage: UpdatableDraftPage, pageId: string, trx?: DrizzleTransaction): Promise<void> {
        return this.updatePages(updatablePage, [pageId], trx);
    }

    async updatePages(updatablePage: UpdatableDraftPage, pageIds: string[], trx?: DrizzleTransaction): Promise<void> {
        if (pageIds.length === 0) {
            return;
        }

        const db = dbOrTx(this.db, trx);
        await db.update(draftPages).set(updatablePage).where(inArray(draftPages.id, pageIds)).execute();
    }

    async insertPage(insertablePage: InsertableDraftPage, trx?: DrizzleTransaction): Promise<DraftPage | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.insert(draftPages).values(insertablePage).returning().execute();
        return result?.[0];
    }

    async insertManyPages(insertablePages: InsertableDraftPage[], trx?: DrizzleTransaction): Promise<DraftPage[]> {
        if (insertablePages.length === 0) {
            return [];
        }

        const db = dbOrTx(this.db, trx);
        const result = await db.insert(draftPages).values(insertablePages).returning().execute();
        return result;
    }

    async deletePage(pageId: string, trx?: DrizzleTransaction): Promise<void> {
        const db = dbOrTx(this.db, trx);
        await db.delete(draftPages).where(eq(draftPages.id, pageId)).execute();
    }

    async deleteManyPages(pageIds: string[], trx?: DrizzleTransaction): Promise<void> {
        if (pageIds.length === 0) {
            return;
        }

        const db = dbOrTx(this.db, trx);
        await db.delete(draftPages).where(inArray(draftPages.id, pageIds)).execute();
    }

    async exists(pageId: string): Promise<boolean> {
        const result = await this.db
            .select({ id: draftPages.id })
            .from(draftPages)
            .where(eq(draftPages.id, pageId))
            .limit(1)
            .execute();

        return result.length > 0;
    }
}
