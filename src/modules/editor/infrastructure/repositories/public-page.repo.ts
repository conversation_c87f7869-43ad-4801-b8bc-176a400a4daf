import { Inject, Injectable } from '@nestjs/common';
import { eq, inArray } from 'drizzle-orm';

import { DRIZZLE_DB } from '../../../../drizzle/drizzle.module';
import { DrizzleDB, DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { dbOrTx } from '../../../../drizzle/drizzle.utils';
import { InsertablePublicPage, PublicPage, publicPages, UpdatablePublicPage } from '../../../../drizzle/schema';
import { IPublicPageRepo } from '../../application/repositories/public-page-repository.interface';

@Injectable()
export class PublicPageRepo implements IPublicPageRepo {
    constructor(@Inject(DRIZZLE_DB) private readonly db: DrizzleDB) {}

    async findById(pageId: string, trx?: DrizzleTransaction): Promise<PublicPage | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.select().from(publicPages).where(eq(publicPages.id, pageId)).limit(1);
        return result[0];
    }

    async findByDocId(docId: string, trx?: DrizzleTransaction): Promise<PublicPage[]> {
        const db = dbOrTx(this.db, trx);
        const result = await db.select().from(publicPages).where(eq(publicPages.docId, docId));
        return result;
    }

    async updatePage(updatablePage: UpdatablePublicPage, pageId: string, trx?: DrizzleTransaction): Promise<void> {
        return this.updatePages(updatablePage, [pageId], trx);
    }

    async updatePages(updatablePage: UpdatablePublicPage, pageIds: string[], trx?: DrizzleTransaction): Promise<void> {
        if (pageIds.length === 0) {
            return;
        }

        const db = dbOrTx(this.db, trx);
        await db.update(publicPages).set(updatablePage).where(inArray(publicPages.id, pageIds)).execute();
    }

    async insertPage(insertablePage: InsertablePublicPage, trx?: DrizzleTransaction): Promise<PublicPage | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.insert(publicPages).values(insertablePage).returning().execute();
        return result?.[0];
    }

    async insertManyPages(insertablePages: InsertablePublicPage[], trx?: DrizzleTransaction): Promise<PublicPage[]> {
        if (insertablePages.length === 0) {
            return [];
        }

        const db = dbOrTx(this.db, trx);
        const result = await db.insert(publicPages).values(insertablePages).returning().execute();
        return result;
    }

    async deletePage(pageId: string, trx?: DrizzleTransaction): Promise<void> {
        const db = dbOrTx(this.db, trx);
        await db.delete(publicPages).where(eq(publicPages.id, pageId)).execute();
    }

    async deleteManyPages(pageIds: string[], trx?: DrizzleTransaction): Promise<void> {
        if (pageIds.length === 0) {
            return;
        }

        const db = dbOrTx(this.db, trx);
        await db.delete(publicPages).where(inArray(publicPages.id, pageIds)).execute();
    }

    async exists(pageId: string): Promise<boolean> {
        const result = await this.db
            .select({ id: publicPages.id })
            .from(publicPages)
            .where(eq(publicPages.id, pageId))
            .limit(1)
            .execute();

        return result.length > 0;
    }
}
