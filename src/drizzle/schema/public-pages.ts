import { jsonb, pgTable, text, uuid, varchar } from 'drizzle-orm/pg-core';

import { temporalMixin } from './mixins/temporal.mixin';

export const publicPages = pgTable('public_pages', {
    id: uuid('id').primaryKey(),
    docId: varchar('doc_id').notNull(),
    content: jsonb('content'),
    textContent: text('text_content'),
    ...temporalMixin,
});

export type PublicPage = typeof publicPages.$inferSelect;
export type InsertablePublicPage = typeof publicPages.$inferInsert;
export type UpdatablePublicPage = Partial<Omit<InsertablePublicPage, 'id'>>;
